# DFE Hello-World Backend 

Implementation based on dfe-hello-world-api specification which provides a Spring REST generated server frame, defining all endpoints and transfer objects.
Further implementations can be performed without direct modification of generated frame classes.

## Code owners

- <PERSON> (@jonathan.schell)
- <PERSON> (@manuel.weht)

## Development
Requires a built dfe-hello-world-api jar as maven dependency. Per default latest stable version is provided as maven dependency. If you want try a snapshot version e.g. when editing API Definition in parallel change dependency to current snapshot version, and make sure to rebuild it before starting the microservice. 

Use spring profile local for local development environment. 

NOTE: *Note that changes in data objects require a rebuild of mapstruct mappers, so a `mvn clean install` is necessary if dataobjects have changed*

NOTE: *Please be aware that the usage of dachser artifactory is required as maven repository in order to build the project. https://artifactory.dach041.dachser.com/ This may be achieved by using a customized local maven settings file*

Example File 

    <settings>
        <servers>
        </servers>
        <mirrors>
            <mirror>
                <id>maven-all</id>
                <name>maven-all</name>
                <url>https://artifactory.dach041.dachser.com:443/artifactory/bint-maven-dfe-all/</url>
                <mirrorOf>*</mirrorOf>
            </mirror>
        </mirrors>
        <profiles>
            <profile>
                <id>artifactory</id>
                <repositories>
                    <repository>
                        <id>maven-all</id>
                        <name>maven-all</name>
                        <url>https://artifactory.dach041.dachser.com:443/artifactory/bint-maven-dfe-all/</url>
                    </repository>
                </repositories>
            </profile>
        </profiles>
        <activeProfiles>
            <activeProfile>artifactory</activeProfile>
        </activeProfiles>
    </settings>

### Spring profiles
- local - local development environment
- local-mssql - local development with mssql (docker) - see src/main/docker/mssql
- dev - dev environment (openShift)
- dev-memory - dev environments automatically created by pull requests in openshift
- test - test environment (openShift)
- production - ... to be defined

**Note:** It is required to configure groups on top level (application.yaml). Feature is used to activate multiple profiles at once.

### Swagger

Swagger UI is deployed as well under http://localhost:8090/swagger-ui/index.html

**Note:** *This is a representation of annotated services, not the openapi.yaml so there might be some differences e.g. name of the schemas*

### MSSQL
There is a docker configuration provided to be used in dev see

    src/main/docker/local-mssql/README.md

### H2
We are now using h2 database in mode REGULAR because in MSSQL Mode prohibits unique indexes containing null values several times. See http://www.h2database.com/html/features.html#compatibility.
We use such an unique index on ```order_base.shipment_number```.


### Liquibase
We are using Liquibase https://docs.liquibase.com/home.html for database versioning. Every change to database structure has to done via changlog under ```/src/main/resources/db/changelog```
Each changeset has to be registered in ```/src/main/resources/db/changelog/db.changelog-master.yaml```, earlier attempts to work with includeAll did lead to problems in sequence of execution over different directories.
We are using .sql files for changesets, because we as team decided it ti be easier to understand.

Further we use liquibase to bootstrap test / mock data into h2 in momory database. This can be used either for 
- local development
- during execution of unit tests
- for environments without dedicated database source

So in case of adaptions to database, it is required to evaluate changes against h2 and mssql.
For database specific changes we use contexts of ```h2``` and ```mssql``` as well es separate folders. Set context in master file application.properties are already prepared for adding these contexts depending on used database.  

## Releases

No manual action is required for release. Everything is done by semantic release and gitlab pipelines. 
As a consequence, every commit must follow the rules of semantic commit messages.
https://github.com/angular/angular/blob/main/CONTRIBUTING.md#-commit-message-format

Please visit https://dil-itd.atlassian.net/wiki/spaces/10172/pages/38866876/Release for further information about the release process.


## Authentication
Currently, connected to keycloak dev instance, login required for most of the API endpoints.
Keycloak can be configured via env settings. 

## Health / status information
Spring actuator is used to provide status information about the running application
- health endpoint http://localhost:8090/mgmt/health
- info endpoint http://localhost:8090/mgmt/info

Info endpoint contains information in json format

    {
        "git": {
            "branch": "feature/DFE-466",
            "commit": {
                "id": "fada320",
                "time": "2022-09-07T07:04:36Z"
            }
        },
        "build": {
            "artifact": "dfe-hello-world-backend",
            "name": "DFE-Hello-World-Backend",
            "time": "2022-09-07T13:38:58.388Z",
            "version": "0.0.1-SNAPSHOT",
            "group": "com.dachser.dfe.hello-world"
        },
        "api": {
            "name": "DFE Hello-World API",
            "version": "0.0.22"
        }
    }

## Connected Systems

List of all connected systems by dfe Hello-World backend: 

| Service Name     | internal service class      | Use case                                                   | Service Type | Example URL                                                               |
|------------------|-----------------------------|------------------------------------------------------------|--------------|---------------------------------------------------------------------------|
| Road OHS service | DFEOpeningHoursService      | Opening times for collection time suggestions              | Hessian      | dach041q.dach041.dachser.com                                              |
| DIB              | DibProducer                 | Publishing avis order data via dib                         | JMS          | nsp://APW041UMST01.dachser-ds.dachser.com:9000                            |
| Road Masterdata  | RoadMasterDataServiceMarker | Masterdata for road, like packaging types, freight terms.. | RestClient   | https://dach041x.dach041.dachser.com:21365/road.masterdata.order.service  |

### EDI

XML Messages are used to export data to edi. For that purpose we use a jaxb auto generated model structure based on a provided xsd file. Generation of classes is integrated in maven process. Mapstruct Mapper is used to convert a ForwardingOrder to a ForwardingOrderInformation.


## Logging

Default logging is logback integrated in spring boot. But log4j can be used as well using ENV Variable LOG4J_CONFIGURATION_FILE to pass a log4j 2 configuration file.  
