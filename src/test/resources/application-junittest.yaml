server:
  port: 9000
spring:
  datasource:
    driver-class-name: org.h2.Driver
    username: sa
    password:
    url: "jdbc:h2:mem:db1;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
  liquibase:
    contexts: mock-data

logging:
  level:
    liquibase.changelog: DEBUG
    com.dachser.dfe: DEBUG
    org.springframework.security: INFO

oAuth:
  server-base-url: "http://localhost/not_set"
