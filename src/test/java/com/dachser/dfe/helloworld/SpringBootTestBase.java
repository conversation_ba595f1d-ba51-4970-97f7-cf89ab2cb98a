package com.dachser.dfe.helloworld;

import com.dachser.dfe.helloworld.jpa.CustomerRepository;
import com.dachser.dfe.helloworld.jpa.entity.Customer;
import com.dachser.dfe.helloworld.model.Division;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;

import static org.mockito.Mockito.when;

@ActiveProfiles("junittest")
@SpringBootTest(classes = { DfeHelloWorldBackendApplication.class })
@EnableAutoConfiguration(exclude = { DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class }) // Disable JPA
public abstract class SpringBootTestBase {

	@MockBean
	protected CustomerRepository customerRepository;

	@BeforeEach
	public void setUpBasicMockedBeanBehaviour() {
		when(customerRepository.findAll()).thenReturn(
				Arrays.asList(new Customer(1l, "12345678", "test", Division.EUROPEAN_LOGISTICS), new Customer(2l, "99999920", "demo", Division.FOOD_LOGISTICS)));
	}
}
