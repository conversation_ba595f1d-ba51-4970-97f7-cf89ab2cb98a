package com.dachser.dfe.helloworld.mapper;

import com.dachser.dfe.helloworld.SpringBootTestBase;
import com.dachser.dfe.helloworld.jpa.entity.Customer;
import com.dachser.dfe.helloworld.model.CustomerDto;
import com.dachser.dfe.helloworld.model.Division;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertEquals;

class CustomerMapperTest extends SpringBootTestBase {

	@Autowired
	private CustomerMapper customerMapper;

	@Test
	void mapToPortDto() {
		Customer entity = new Customer(1l, "12345678", "test", Division.EUROPEAN_LOGISTICS);
		CustomerDto dto = customerMapper.mapToCustomerDto(entity);

		assertEquals(dto.getCustomerNumber(), entity.getCustomerNumber());
		assertEquals(dto.getLabel(), entity.getLabel());
		assertEquals(dto.getDivision(), CustomerDto.DivisionEnum.EUROPEAN_LOGISTICS);
	}


}
