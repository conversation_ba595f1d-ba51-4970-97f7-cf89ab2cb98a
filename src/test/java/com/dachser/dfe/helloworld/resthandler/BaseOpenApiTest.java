package com.dachser.dfe.helloworld.resthandler;

import com.dachser.dfe.helloworld.DfeHelloWorldBackendApplication;
import com.dachser.dfe.helloworld.SpringBootTestBase;
import io.restassured.module.mockmvc.RestAssuredMockMvc;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.time.Clock;
import java.time.Instant;

import static org.mockito.Mockito.when;

@AutoConfigureMockMvc
@SpringBootTest(classes = DfeHelloWorldBackendApplication.class)
@EnableAutoConfiguration(exclude = { DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class })  // Disable JPA
public abstract class BaseOpenApiTest extends SpringBootTestBase {

	@Value("${openapi.dFEQuote.base-path:}")
	private String basePath;

	@Autowired
	private MockMvc mockMvc;

	@MockBean
	private Clock clock;

	private static final String VERSION_PREFIX = "/v1";

	@BeforeEach
	void basicSetUp() {
		when(clock.instant()).thenReturn(Instant.EPOCH);
		RestAssuredMockMvc.mockMvc(mockMvc);
	}

	public String buildUrl(final String urlPath) {
		return basePath + VERSION_PREFIX + urlPath;
	}
}
