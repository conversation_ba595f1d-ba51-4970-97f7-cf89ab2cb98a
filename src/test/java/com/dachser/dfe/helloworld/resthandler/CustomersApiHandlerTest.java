package com.dachser.dfe.helloworld.resthandler;

import com.dachser.dfe.helloworld.model.CustomerDto;
import io.restassured.common.mapper.TypeRef;
import io.restassured.module.mockmvc.response.MockMvcResponse;
import io.restassured.module.mockmvc.specification.MockMvcRequestSpecification;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.List;

import static io.restassured.module.mockmvc.RestAssuredMockMvc.given;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CustomersApiHandlerTest extends BaseOpenApiTest {

	@Nested
	@WithMockUser
	class GetCustomers {
		@Test
		void shouldReturn200() {
			final MockMvcRequestSpecification request = given().header("Content-Type", "application/json");
			final MockMvcResponse response = request.get(buildUrl("/customers"));
			assertEquals(HttpStatus.SC_OK, response.statusCode());
			final List<CustomerDto> customers = response.getBody().as(new TypeRef<>() {
			});
			assertFalse(customers.isEmpty());
			customers.forEach(customer -> {
				assertTrue(StringUtils.isNotEmpty(customer.getCustomerNumber()));
				assertTrue(StringUtils.isNotEmpty(customer.getLabel()));
				assertNotNull(customer.getDivision());
			});
		}
	}

}
