package com.dachser.dfe.helloworld.resthandler;

import com.dachser.dfe.helloworld.api.CustomersApiDelegate;
import com.dachser.dfe.helloworld.model.CustomerDto;
import com.dachser.dfe.helloworld.service.CustomersService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class CustomersApiHandler implements CustomersApiDelegate {

	private final CustomersService customersService;

	@Override
	public ResponseEntity<List<CustomerDto>> getCustomers() {
		return ResponseEntity.ok(customersService.getCustomers());
	}
}
