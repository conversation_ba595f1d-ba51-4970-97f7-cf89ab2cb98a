package com.dachser.dfe.helloworld.mapper;

import com.dachser.dfe.helloworld.model.CustomerDto;
import com.dachser.dfe.helloworld.model.Division;
import org.mapstruct.Mapper;
import org.mapstruct.ValueMapping;

@Mapper(componentModel = "spring")
public interface DivisionMapper {

	@ValueMapping(source = "EUROPEAN_LOGISTICS", target = "EUROPEAN_LOGISTICS")
	@ValueMapping(source = "FOOD_LOGISTICS", target = "FOOD_LOGISTICS")
	CustomerDto.DivisionEnum toDto(final Division division);

}
