package com.dachser.dfe.helloworld.mapper;

import com.dachser.dfe.helloworld.jpa.entity.Customer;
import com.dachser.dfe.helloworld.model.CustomerDto;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", uses =  { DivisionMapper.class })
public interface CustomerMapper {

	CustomerDto mapToCustomerDto(final Customer customer);

	List<CustomerDto> mapToCustomerDtos(final List<Customer> customers);

}
