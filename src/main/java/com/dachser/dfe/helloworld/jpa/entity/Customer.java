package com.dachser.dfe.helloworld.jpa.entity;

import com.dachser.dfe.helloworld.model.Division;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Customer {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false, updatable = false)
	private Long id;

	@Column(name = "customer_number", length = 8)
	private String customerNumber;

	@Column(name = "label", length = 30)
	private String label;

	@Column(name = "division", length = 18)
	@Enumerated(EnumType.STRING)
	private Division division;

}
