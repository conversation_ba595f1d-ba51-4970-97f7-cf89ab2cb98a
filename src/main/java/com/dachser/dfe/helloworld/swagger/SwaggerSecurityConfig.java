package com.dachser.dfe.helloworld.swagger;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.OAuthFlow;
import io.swagger.v3.oas.annotations.security.OAuthFlows;
import io.swagger.v3.oas.annotations.security.SecurityScheme;

/**
 * Swagger configuration class for swagger ui generated via springdoc.
 */
@SecurityScheme(name = "dachserOAuth", type = SecuritySchemeType.OAUTH2,
		flows = @OAuthFlows(authorizationCode = @OAuthFlow(
				authorizationUrl = "${springdoc.oAuthFlow.authorizationUrl}"
				, tokenUrl = "${springdoc.oAuthFlow.tokenUrl}")))
@OpenAPIDefinition(info = @Info(version = "${springdoc.app.version}", title = "${springdoc.app.title}", description = "${springdoc.app.description}"))
public class SwaggerSecurityConfig {
}
