package com.dachser.dfe.helloworld.i18n;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;

@Configuration
public class LocaleConfiguration {
	
    @Bean
    public LocaleResolver localeResolver() {
        return new DfeLocaleResolver();
    }
    
    @Bean
    public ResourceBundleMessageSource messageSource() {
        final ResourceBundleMessageSource rs = new ResourceBundleMessageSource();
        rs.setBasename("locales/messages");
        rs.setDefaultEncoding("UTF-8");
        rs.setUseCodeAsDefaultMessage(true);
        return rs;
    }
}
