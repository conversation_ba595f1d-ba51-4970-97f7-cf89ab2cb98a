package com.dachser.dfe.helloworld.i18n;

import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

public class DfeLocaleResolver extends AcceptHeaderLocaleResolver {

	@Value("${languages.supported}")
	private List<String> supportedLanguagesKeys;

	@Value("${languages.default}")
	private String defaultLanguageKey;

	@NotNull
	@Override
	public Locale resolveLocale(final HttpServletRequest request) {

		final Locale defaultLocale = Locale.forLanguageTag(defaultLanguageKey);
		this.setDefaultLocale(defaultLocale);

		final List<Locale> supportedLocales = supportedLanguagesKeys.stream().map(Locale::forLanguageTag).collect(Collectors.toList());

		final String acceptLanguage = request.getHeader(HttpHeaders.ACCEPT_LANGUAGE);
		if (!StringUtils.isBlank(acceptLanguage)) {
			final List<Locale.LanguageRange> languageRange = Locale.LanguageRange.parse(acceptLanguage);

			final Locale supportedLocale = Locale.lookup(languageRange, supportedLocales);
			if (supportedLocale != null) {
				return supportedLocale;
			}
		}

		return defaultLocale;
	}
}
