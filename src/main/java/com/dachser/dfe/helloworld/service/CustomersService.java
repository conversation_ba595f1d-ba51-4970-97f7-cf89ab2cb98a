package com.dachser.dfe.helloworld.service;

import com.dachser.dfe.helloworld.jpa.CustomerRepository;
import com.dachser.dfe.helloworld.mapper.CustomerMapper;
import com.dachser.dfe.helloworld.model.CustomerDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CustomersService {

	private final CustomerRepository customerRepository;

	private final CustomerMapper customerMapper;

	public List<CustomerDto> getCustomers() {
		return customerMapper.mapToCustomerDtos(customerRepository.findAll());
	}

}
