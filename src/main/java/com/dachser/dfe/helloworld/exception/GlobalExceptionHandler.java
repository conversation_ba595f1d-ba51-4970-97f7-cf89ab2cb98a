package com.dachser.dfe.helloworld.exception;

import jakarta.persistence.PersistenceException;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;


/**
 * This is the central place where exceptions are caught and translated in the appropriate problem responses.
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

	@ExceptionHandler(value = { BadRequestException.class, ValidationException.class, ConstraintViolationException.class })
	protected ResponseEntity<Object> handleBadRequest(final RuntimeException ex, final WebRequest request) {
		final String bodyOfResponse = ex.getMessage();

		return handleExceptionInternal(ex, bodyOfResponse, new HttpHeaders(), HttpStatus.BAD_REQUEST, request);
	}

	@ExceptionHandler(value = { NotAuthenticatedException.class })
	protected ResponseEntity<Object> handleUnauthorizedRequest(final RuntimeException ex, final WebRequest request) {
		final String bodyOfResponse = ex.getMessage();

		return handleExceptionInternal(ex, bodyOfResponse, new HttpHeaders(), HttpStatus.UNAUTHORIZED, request);
	}

	@ExceptionHandler(value = { NotAuthorizedException.class })
	protected ResponseEntity<Object> handleForbiddenRequest(final RuntimeException ex, final WebRequest request) {
		final String bodyOfResponse = ex.getMessage();

		return handleExceptionInternal(ex, bodyOfResponse, new HttpHeaders(), HttpStatus.FORBIDDEN, request);
	}

	@ExceptionHandler(value = { PersistenceException.class, IllegalArgumentException.class })
	protected ResponseEntity<Object> handleServerError(final RuntimeException ex, final WebRequest request) {
		final String bodyOfResponse = ex.getMessage();

		return handleExceptionInternal(ex, bodyOfResponse, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, request);
	}

}
