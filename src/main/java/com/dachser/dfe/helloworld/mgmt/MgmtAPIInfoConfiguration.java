package com.dachser.dfe.helloworld.mgmt;

import com.dachser.dfe.helloworld.DfeHelloWorldBackendApplication;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.boot.actuate.info.MapInfoContributor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class MgmtAPIInfoConfiguration {

	@Bean
	InfoContributor getInfoContributor() {
		final Map<String, Object> details = new HashMap<>();
		details.put("name", "DFE quote API");
		details.put("version", DfeHelloWorldBackendApplication.class.getPackage().getImplementationVersion());
		final Map<String, Object> wrapper = new HashMap<>();
		wrapper.put("api", details);
		return new MapInfoContributor(wrapper);
	}
}
