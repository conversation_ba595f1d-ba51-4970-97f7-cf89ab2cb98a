package com.dachser.dfe.helloworld.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.web.SecurityFilterChain;

@Profile("local")
@Configuration
@Order(101)
public class LocalSecurityConfig {

	@Bean
	public SecurityFilterChain securityFilterChainH2(final HttpSecurity http) throws Exception {
		// @formatter:off
		// required to be able to use h2 console in local profile
		http.securityMatcher("/h2-console*/*")
				.authorizeHttpRequests(authz -> authz.anyRequest().permitAll())
				.csrf(AbstractHttpConfigurer::disable)
				.headers(headers -> headers.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable));
		// @formatter:on
		return http.getOrBuild();
	}
}
