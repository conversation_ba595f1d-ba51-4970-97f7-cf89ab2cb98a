package com.dachser.dfe.helloworld.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.web.util.matcher.AntPathRequestMatcher.antMatcher;

@Order(100)
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class OAuth2ResourceSecurity {

	@Value("${spring.security.oauth2.resource-server.dachserOAuth.jwk-set-uri}")
	private String jwkSetUri;

	/**
	 * All paths are protected by default in current configuration, exclusions have to be configured per path.
	 */
	@Bean
	public SecurityFilterChain securityFilterChain(final HttpSecurity http) throws Exception {
		// @formatter:off
		http.authorizeHttpRequests(requests -> requests
						.requestMatchers(antMatcher("^/$")).permitAll()
						.requestMatchers(antMatcher("/mgmt/**")).permitAll()
						.requestMatchers(antMatcher("/v3/**")).permitAll()
						.requestMatchers(antMatcher("/swagger-ui*/*")).permitAll()
						.requestMatchers(antMatcher("/swagger-ui.html")).permitAll()
						.anyRequest().authenticated())
				.oauth2ResourceServer(httpSecurityOAuth2ResourceServerConfigurer -> httpSecurityOAuth2ResourceServerConfigurer.jwt(Customizer.withDefaults()))
				.cors(Customizer.withDefaults());
		// @formatter:on
		return http.getOrBuild();
	}

	@Bean
	JwtDecoder jwtDecoder() {
		return NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
	}

}