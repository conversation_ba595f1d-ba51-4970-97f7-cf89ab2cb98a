server:
  servlet:
    context-path: /
management:
  endpoint:
    health:
      show-components: always
    info:
      enabled: true
    caches:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,caches,caches-cache
      base-path: /mgmt

oAuth:
  server-base-url: ${ENV_HELLO_WORLD_OAUTH_SERVER_BASE_URL}


spring:
  profiles:
    active: "${variables.spring.profiles.active:}"
    group:
      # Example implementation as demonstration for hello world project
      local:
        - "local"
        - "com.dachser.road.masterdata.restapi"
      local-mssql:
        - "local"
        - "local-mssql"

  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    username: ${ENV_HELLO_WORLD_DB_USER}
    password: ${ENV_HELLO_WORLD_DB_PASS}
    url: ${ENV_HELLO_WORLD_DATASOURCE_URL}
    hikari:
      maximum-pool-size: 50
      register-mbeans: true
      minimum-idle: 4
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.yaml
    # Default context, replace with mock-data / h2 if you need Mocks or in-memory database
    contexts: mssql
  # Example implementation
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 25MB
  jpa:
    hibernate:
      ddl-auto: none
  h2:
    console:
      enabled: false

  security:
    oauth2:
      resource-server:
        dachserOAuth:
          jwk-set-uri: ${oAuth.server-base-url}/certs

languages:
  supported: de,en
  default: en

# https://springdoc.org/properties.html
springdoc:
  app:
    version: @project.version@
    title: DFE hello-world API
    description: REST Interface DFE Hello World
  swagger-ui:
    oauth:
      client-id: dfe-frontend
      use-pkce-with-authorization-code-grant: true
  oAuthFlow:
    authorizationUrl: ${oAuth.server-base-url}/auth
    tokenUrl: ${oAuth.server-base-url}/token

