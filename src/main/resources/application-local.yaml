server:
  port: 8090
logging:
  level:
    org.springframework.security.oauth2: DEBUG
    web: DEBUG
    org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: TRACE
    liquibase.changelog: DEBUG
    com.dachser.dfe: DEBUG

spring:
  config:
    activate:
      on-profile: local
  datasource:
    driver-class-name: org.h2.Driver
    username: sa
    password:
    #url: "jdbc:h2:./testdb/h2testdb;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
    url: "jdbc:h2:mem:db1;MODE=MSSQLServer;DB_CLOSE_ON_EXIT=FALSE;INIT=CREATE SCHEMA IF NOT EXISTS dbo\\;SET SCHEMA dbo"
  h2:
    console:
      enabled: true
      path: /h2-console
  liquibase:
    contexts: mock-data,h2
  jpa:
    properties:
      hibernate:
        # as long as we use h2 in MSSQL mode we need to use the SQLServerDialect
        dialect: org.hibernate.dialect.SQLServerDialect

oAuth:
  server-base-url: "https://login.platform-dev.dach041.dachser.com/realms/dfe/protocol/openid-connect"
